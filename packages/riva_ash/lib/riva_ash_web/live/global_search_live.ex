defmodule RivaAshWeb.GlobalSearchLive do
  @moduledoc """
  Global search LiveView for unregistered users to find businesses and items.
  """
  use RivaAshWeb, :live_view

  import RivaAshWeb.Components.Atoms.Button
  import RivaAshWeb.Components.Atoms.Input

  alias RivaAsh.Resources.{Business, Item}

  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:search_term, "")
      |> assign(:businesses, [])
      |> assign(:items, [])
      |> assign(:loading, false)
      |> assign(:searched, false)

    {:ok, socket}
  end

  def handle_params(params, _url, socket) do
    search_term = params["q"] || ""
    
    socket = 
      socket
      |> assign(:search_term, search_term)
      |> maybe_perform_search(search_term)

    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"term" => search_term}}, socket) do
    # Update URL with search term
    {:noreply, push_patch(socket, to: ~p"/search?q=#{search_term}")}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_term, "")
      |> assign(:businesses, [])
      |> assign(:items, [])
      |> assign(:searched, false)

    {:noreply, push_patch(socket, to: ~p"/search")}
  end

  def handle_event("view_business", %{"id" => business_id}, socket) do
    # Navigate to business detail page (we'll create this later)
    {:noreply, push_navigate(socket, to: ~p"/business/#{business_id}")}
  end

  def handle_event("view_item", %{"id" => item_id}, socket) do
    # Navigate to item detail page (we'll create this later)  
    {:noreply, push_navigate(socket, to: ~p"/item/#{item_id}")}
  end

  defp maybe_perform_search(socket, search_term) when search_term != "" do
    socket
    |> assign(:loading, true)
    |> start_async(:search, fn -> perform_search(search_term) end)
  end

  defp maybe_perform_search(socket, _search_term) do
    socket
    |> assign(:businesses, [])
    |> assign(:items, [])
    |> assign(:searched, false)
  end

  def handle_async(:search, {:ok, {businesses, items}}, socket) do
    socket =
      socket
      |> assign(:businesses, businesses)
      |> assign(:items, items)
      |> assign(:loading, false)
      |> assign(:searched, true)

    {:noreply, socket}
  end

  def handle_async(:search, {:exit, reason}, socket) do
    socket =
      socket
      |> assign(:businesses, [])
      |> assign(:items, [])
      |> assign(:loading, false)
      |> assign(:searched, true)
      |> put_flash(:error, "Search failed: #{inspect(reason)}")

    {:noreply, socket}
  end

  defp perform_search(search_term) do
    # Search businesses
    businesses_result = Business
    |> Ash.Query.for_read(:public_search, %{search_term: search_term})
    |> Ash.read(domain: RivaAsh.Domain)

    # Search items  
    items_result = Item
    |> Ash.Query.for_read(:public_search, %{search_term: search_term})
    |> Ash.Query.load([:business])
    |> Ash.read(domain: RivaAsh.Domain)

    businesses = case businesses_result do
      {:ok, businesses} -> businesses
      _ -> []
    end

    items = case items_result do
      {:ok, items} -> items
      _ -> []
    end

    {businesses, items}
  end

  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Header -->
      <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div class="flex items-center">
              <h1 class="text-3xl font-bold text-gray-900">RivaAsh</h1>
              <span class="ml-2 text-sm text-gray-500">Global Search</span>
            </div>
            <div class="flex items-center space-x-4">
              <.link navigate={~p"/users/sign_in"} class="text-blue-600 hover:text-blue-500">
                Sign In
              </.link>
              <.link navigate={~p"/users/register"} class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                Sign Up
              </.link>
            </div>
          </div>
        </div>
      </div>

      <!-- Search Section -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-8">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Find Your Perfect Reservation</h2>
          <p class="text-xl text-gray-600 mb-8">Search across thousands of businesses and available items</p>
          
          <!-- Search Form -->
          <.form for={%{}} as={:search} phx-submit="search" class="max-w-2xl mx-auto">
            <div class="flex gap-4">
              <div class="flex-1">
                <.input
                  type="text"
                  name="term"
                  value={@search_term}
                  placeholder="Search businesses, items, or services..."
                  class="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <.button type="submit" class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50" disabled={@loading}>
                <%= if @loading do %>
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching...
                <% else %>
                  Search
                <% end %>
              </.button>
              <%= if @search_term != "" do %>
                <.button type="button" phx-click="clear_search" variant="outline" class="px-4 py-3">
                  Clear
                </.button>
              <% end %>
            </div>
          </.form>
        </div>

        <!-- Results Section -->
        <%= if @searched do %>
          <div class="mt-12">
            <%= if @businesses == [] and @items == [] do %>
              <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                  <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p class="text-gray-500">Try adjusting your search terms or browse all available businesses.</p>
              </div>
            <% else %>
              <!-- Businesses Results -->
              <%= if @businesses != [] do %>
                <div class="mb-12">
                  <h3 class="text-2xl font-bold text-gray-900 mb-6">Businesses (<%= length(@businesses) %>)</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <%= for business <- @businesses do %>
                      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer" phx-click="view_business" phx-value-id={business.id}>
                        <div class="p-6">
                          <h4 class="text-xl font-semibold text-gray-900 mb-2"><%= business.name %></h4>
                          <%= if business.public_description do %>
                            <p class="text-gray-600 mb-4"><%= business.public_description %></p>
                          <% end %>
                          <div class="flex items-center text-sm text-gray-500">
                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            Business
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>

              <!-- Items Results -->
              <%= if @items != [] do %>
                <div>
                  <h3 class="text-2xl font-bold text-gray-900 mb-6">Available Items (<%= length(@items) %>)</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <%= for item <- @items do %>
                      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer" phx-click="view_item" phx-value-id={item.id}>
                        <div class="p-6">
                          <h4 class="text-xl font-semibold text-gray-900 mb-2"><%= item.name %></h4>
                          <%= if item.public_description do %>
                            <p class="text-gray-600 mb-4"><%= item.public_description %></p>
                          <% end %>
                          <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                              <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                              </svg>
                              Item
                            </div>
                            <%= if item.business do %>
                              <span class="text-sm text-blue-600 font-medium"><%= item.business.name %></span>
                            <% end %>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
